# 房间管理功能说明

## 功能概述

房间管理是IoT物联网系统的核心功能之一，支持用户创建、管理房间，并将设备分配到不同的房间中。房间支持层级结构，可以创建房间分组。

## 功能特性

### 1. 房间基本管理
- 创建房间：支持创建新的房间
- 编辑房间：修改房间名称、类型等信息
- 删除房间：删除不需要的房间（需要先移除子房间）
- 查看房间：查看房间详细信息

### 2. 房间层级结构
- 支持父子房间关系
- 可以创建房间分组（如：我的家 > 客厅）
- 树形展示房间结构

### 3. 房间类型
支持多种房间类型：
- `living_room` - 客厅
- `bedroom` - 卧室
- `kitchen` - 厨房
- `bathroom` - 卫生间
- `study` - 书房
- `balcony` - 阳台
- 自定义类型

## 数据库设计

### iot_room 表结构
```sql
CREATE TABLE `iot_room` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '房间ID',
  `name` varchar(64) NOT NULL COMMENT '房间名称',
  `type` varchar(32) NULL DEFAULT NULL COMMENT '房间类型',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父级房间ID',
  `user_id` bigint NOT NULL COMMENT '所属用户ID',
  `creator` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id`(`user_id`),
  INDEX `idx_parent_id`(`parent_id`),
  INDEX `idx_name`(`name`)
);
```

## 后端API接口

### 1. 房间管理接口

#### 创建房间
- **接口**: `POST /iot/room/create`
- **权限**: `iot:room:create`
- **参数**: IotRoomSaveReqVO
```json
{
  "name": "客厅",
  "type": "living_room",
  "parentId": 1,
  "userId": 1
}
```

#### 更新房间
- **接口**: `PUT /iot/room/update`
- **权限**: `iot:room:update`
- **参数**: IotRoomSaveReqVO

#### 删除房间
- **接口**: `DELETE /iot/room/delete?id={id}`
- **权限**: `iot:room:delete`

#### 获取房间详情
- **接口**: `GET /iot/room/get?id={id}`
- **权限**: `iot:room:query`

#### 获取房间分页列表
- **接口**: `GET /iot/room/page`
- **权限**: `iot:room:query`
- **参数**: IotRoomPageReqVO

#### 获取用户房间列表
- **接口**: `GET /iot/room/list-by-user-id?userId={userId}`
- **权限**: `iot:room:query`

#### 获取子房间列表
- **接口**: `GET /iot/room/list-by-parent-id?parentId={parentId}`
- **权限**: `iot:room:query`

#### 获取用户的子房间列表
- **接口**: `GET /iot/room/list-by-user-and-parent?userId={userId}&parentId={parentId}`
- **权限**: `iot:room:query`

#### 导出房间Excel
- **接口**: `GET /iot/room/export-excel`
- **权限**: `iot:room:export`

## 前端页面

### 1. 房间管理页面 (`pages/room/index.vue`)
- 房间列表展示
- 搜索功能
- 新增/编辑房间
- 删除房间
- 分页功能

### 2. 房间选择页面 (`pages/room/select.vue`)
- 树形结构展示房间
- 房间搜索
- 房间选择功能

### 3. 房间树组件 (`components/room-tree.vue`)
- 可复用的房间树形组件
- 支持展开/收起
- 支持选择功能

## 使用示例

### 1. 创建房间层级结构
```
我的家
├── 客厅
├── 卧室
│   ├── 主卧
│   └── 次卧
├── 厨房
└── 卫生间
```

### 2. 前端调用示例
```javascript
import { createRoom, getRoomListByUserId } from '@/api/iot/room'

// 创建房间
const roomData = {
  name: '客厅',
  type: 'living_room',
  parentId: 1,
  userId: 1
}
await createRoom(roomData)

// 获取用户房间列表
const rooms = await getRoomListByUserId(1)
```

## 权限配置

需要在系统中配置以下权限：
- `iot:room:create` - 创建房间
- `iot:room:update` - 更新房间
- `iot:room:delete` - 删除房间
- `iot:room:query` - 查询房间
- `iot:room:export` - 导出房间

## 注意事项

1. **房间名称唯一性**: 同一用户下的房间名称必须唯一
2. **删除限制**: 有子房间的房间不能直接删除，需要先删除子房间
3. **父级房间验证**: 设置父级房间时会验证父级房间是否存在
4. **用户隔离**: 房间数据按用户隔离，用户只能管理自己的房间
5. **租户隔离**: 支持多租户，房间数据按租户隔离

## 扩展功能

后续可以扩展的功能：
1. 房间图标设置
2. 房间背景图片
3. 房间温度、湿度等环境数据
4. 房间设备统计
5. 房间场景联动
6. 房间分享功能
