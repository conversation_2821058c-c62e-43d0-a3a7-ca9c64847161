# IoT用户Token管理机制

## 概述

本系统采用OAuth2.0标准管理IoT用户的访问令牌，确保用户登录状态的安全性和可控性。

## Token管理流程

### 1. 用户登录流程

当IoT用户登录时，系统会执行以下步骤：

1. **验证用户凭据**：检查用户名、密码和用户状态
2. **创建OAuth2 Token**：
   - 生成唯一的`access_token`和`refresh_token`
   - 将token信息保存到数据库表：
     - `system_oauth2_access_token`：存储访问令牌
     - `system_oauth2_refresh_token`：存储刷新令牌
   - 同时缓存到Redis中提高访问性能
3. **返回登录响应**：包含用户信息和access_token

### 2. Token存储结构

#### system_oauth2_access_token 表
```sql
CREATE TABLE `system_oauth2_access_token` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `user_type` tinyint NOT NULL COMMENT '用户类型',
  `user_info` varchar(512) NOT NULL COMMENT '用户信息',
  `access_token` varchar(255) NOT NULL COMMENT '访问令牌',
  `refresh_token` varchar(32) NOT NULL COMMENT '刷新令牌',
  `client_id` varchar(255) NOT NULL COMMENT '客户端编号',
  `scopes` varchar(255) DEFAULT NULL COMMENT '授权范围',
  `expires_time` datetime NOT NULL COMMENT '过期时间',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  INDEX `idx_access_token`(`access_token`),
  INDEX `idx_refresh_token`(`refresh_token`)
) COMMENT = 'OAuth2 访问令牌';
```

#### system_oauth2_refresh_token 表
```sql
CREATE TABLE `system_oauth2_refresh_token` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户编号',
  `refresh_token` varchar(32) NOT NULL COMMENT '刷新令牌',
  `user_type` tinyint NOT NULL COMMENT '用户类型',
  `client_id` varchar(255) NOT NULL COMMENT '客户端编号',
  `scopes` varchar(255) DEFAULT NULL COMMENT '授权范围',
  `expires_time` datetime NOT NULL COMMENT '过期时间',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`)
) COMMENT = 'OAuth2 刷新令牌';
```

### 3. 用户退出登录流程

当IoT用户退出登录时，系统会执行以下步骤：

1. **获取当前Token**：从HTTP请求中提取Authorization头中的token
2. **删除Token数据**：
   - 从`system_oauth2_access_token`表删除对应的access_token记录
   - 从`system_oauth2_refresh_token`表删除对应的refresh_token记录
   - 从Redis缓存中删除token
3. **清理用户状态**：清除前端的用户信息和token

## 代码实现

### 登录实现
```java
@Override
public IotUserLoginRespVO login(IotUserLoginReqVO reqVO) {
    // 1. 验证用户凭据
    IotUserDO user = validateUserCredentials(reqVO);
    
    // 2. 创建OAuth2 token
    OAuth2AccessTokenCreateReqDTO reqDTO = new OAuth2AccessTokenCreateReqDTO();
    reqDTO.setUserId(user.getId());
    reqDTO.setUserType(UserTypeEnum.MEMBER.getValue());
    reqDTO.setClientId(OAuth2ClientConstants.CLIENT_ID_DEFAULT);
    
    OAuth2AccessTokenRespDTO tokenResp = oauth2TokenApi.createAccessToken(reqDTO);
    
    // 3. 返回登录响应
    IotUserLoginRespVO respVO = new IotUserLoginRespVO();
    respVO.setToken(tokenResp.getAccessToken());
    // ... 设置其他用户信息
    return respVO;
}
```

### 退出登录实现
```java
@Override
public void logout() {
    // 1. 获取当前请求的token
    String accessToken = getCurrentAccessToken();
    
    if (accessToken != null) {
        // 2. 删除token数据
        oauth2TokenApi.removeAccessToken(accessToken);
    }
}
```

## 安全特性

1. **Token唯一性**：每个token都是全局唯一的UUID
2. **过期机制**：token有过期时间，过期后需要重新登录
3. **数据库持久化**：token信息保存在数据库中，便于管理和审计
4. **Redis缓存**：提高token验证性能
5. **租户隔离**：支持多租户环境下的token隔离

## 数据库查询示例

### 查看当前活跃的token
```sql
SELECT 
    at.id,
    at.user_id,
    at.access_token,
    at.expires_time,
    at.create_time
FROM system_oauth2_access_token at
WHERE at.expires_time > NOW()
  AND at.deleted = 0
ORDER BY at.create_time DESC;
```

### 查看特定用户的token
```sql
SELECT 
    at.access_token,
    at.expires_time,
    rt.refresh_token
FROM system_oauth2_access_token at
LEFT JOIN system_oauth2_refresh_token rt ON at.refresh_token = rt.refresh_token
WHERE at.user_id = ? 
  AND at.deleted = 0;
```

### 清理过期token
```sql
DELETE FROM system_oauth2_access_token 
WHERE expires_time < NOW();

DELETE FROM system_oauth2_refresh_token 
WHERE expires_time < NOW();
```

## 注意事项

1. **Token安全**：前端应妥善保存token，避免泄露
2. **定期清理**：建议定期清理过期的token数据
3. **监控告警**：可以监控token创建和删除的频率，发现异常情况
4. **日志记录**：系统会记录token的创建和删除操作，便于审计 