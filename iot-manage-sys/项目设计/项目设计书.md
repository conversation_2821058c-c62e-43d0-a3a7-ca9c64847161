一、ER图（实体关系图）
1. 文字描述
用户（User） 1对多 房间（Room）
房间（Room） 1对多 设备（Device）
设备（Device） 1对多 设备数据（DeviceData，TDengine存储）
用户（User） 1对多 场景（Scene）
场景（Scene） 1对多 条件（Condition）
场景（Scene） 1对多 动作（Action）
设备（Device） 1对多 闹钟（Alarm）
设备（Device） 1对多 红外码（InfraredCode）
用户（User） 1对多 充值/消费记录（Transaction）
2. Mermaid ER图
USER
ROOM
DEVICE
DEVICEDATA
SCENE
CONDITION
ACTION
ALARM
INFRAREDCODE
TRANSACTION
has
has
reports
owns
has
triggers
has
has
makes
二、接口文档（RESTful API）
1. 用户管理
GET /api/users 查询用户列表
POST /api/users 新增用户
PUT /api/users/{id} 修改用户
DELETE /api/users/{id} 删除用户
POST /api/auth/login 用户登录
2. 房间管理
GET /api/rooms 查询房间列表（支持分组/树结构）
POST /api/rooms 新增房间
PUT /api/rooms/{id} 修改房间
DELETE /api/rooms/{id} 删除房间
3. 设备管理
GET /api/devices 查询设备列表
POST /api/devices 新增设备
PUT /api/devices/{id} 修改设备
DELETE /api/devices/{id} 删除设备
GET /api/devices/{id}/status 查询设备状态
POST /api/devices/{id}/command 下发设备指令（MQTT）
4. 场景管理
GET /api/scenes 查询场景列表
POST /api/scenes 新增场景
PUT /api/scenes/{id} 修改场景
DELETE /api/scenes/{id} 删除场景
5. 条件管理
GET /api/conditions?sceneId=xxx 查询条件列表
POST /api/conditions 新增条件
PUT /api/conditions/{id} 修改条件
DELETE /api/conditions/{id} 删除条件
6. 动作管理
GET /api/actions?sceneId=xxx 查询动作列表
POST /api/actions 新增动作
PUT /api/actions/{id} 修改动作
DELETE /api/actions/{id} 删除动作
7. 闹钟管理
GET /api/alarms?deviceId=xxx 查询闹钟列表
POST /api/alarms 新增闹钟
PUT /api/alarms/{id} 修改闹钟
DELETE /api/alarms/{id} 删除闹钟
8. 红外码管理
GET /api/infrared-codes?deviceId=xxx 查询红外码列表
POST /api/infrared-codes 新增红外码
PUT /api/infrared-codes/{id} 修改红外码
DELETE /api/infrared-codes/{id} 删除红外码
9. 充值/消费记录
GET /api/transactions?userId=xxx 查询记录
POST /api/transactions 新增记录
10. 设备数据查询（TDengine）
GET /api/device-data?deviceId=xxx&type=temperature&start=xxx&end=xxx 查询设备时序数据
三、所有模块详细设计
1. 用户管理
功能：注册、登录、权限、VIP等级、充值消费
技术要点：JWT鉴权、密码加密、角色权限、VIP到期自动降级
2. 房间管理
功能：房间分组、树形结构、房间下设备管理
技术要点：递归查询、树结构存储、用户隔离
3. 设备管理
功能：设备增删改查、状态监控、MQTT通信、设备分组
技术要点：MQTT消息收发、设备上下线、设备状态缓存、设备与房间/用户关联
4. 场景管理
功能：场景定义、场景触发、场景联动
技术要点：场景与条件/动作关联、场景触发机制（定时/事件）、联动执行
5. 条件管理
功能：条件配置（如温度>30℃）、多条件组合
技术要点：条件表达式解析、与TDengine数据联动、条件触发事件
6. 动作管理
功能：动作配置（如开空调）、多动作联动
技术要点：动作下发MQTT、动作执行日志、失败重试
7. 闹钟管理
功能：定时任务、周期任务、设备定时控制
技术要点：定时调度（如Quartz）、MQTT下发、任务状态管理
8. 红外码管理
功能：红外码学习、下发、管理
技术要点：红外码存储、设备红外学习/下发协议
9. 充值/消费管理
功能：充值、消费、余额、VIP升级
技术要点：事务一致性、支付接口对接、余额校验
10. 设备数据管理（TDengine）
功能：时序数据采集、存储、查询、统计
技术要点：MQTT数据入库、TDengine超级表、数据聚合分析
四、MQTT与TDengine集成说明
MQTT：设备通过MQTT上报数据，平台订阅相关topic，收到数据后写入TDengine和MySQL（如状态、告警）。
TDengine：用于存储高频时序数据（如温湿度、电量），MySQL只存设备、场景等结构化业务数据。