物联网后台管理系统设计文档
一、系统架构
前端：ruoyi-vue-pro（Vue3/Ant Design）
后端：Spring Boot + MyBatis Plus
数据库：MySQL（业务数据）、TDengine（时序数据）
消息协议：MQTT（设备通信）
其他：Redis、Spring Security、JWT、Docker

二、主要功能模块
1.房间子系统
房间管理（分组）
设备管理
1.场景子系统
场景管理
条件管理
1.动作管理
1.内置闹钟子系统
内置闹钟管理
红外功能
1.用户子系统
用户管理（VIP）
1.运营子系统
收费管理
充值/消费记录














一、用户管理模块
1. 字段设计（user）
字段名	类型	说明
id	bigint	主键
username	varchar(64)	用户名
password	varchar(128)	密码（加密）
vip_level	tinyint	VIP等级
status	tinyint	状态
phone	varchar(20)	手机号
email	varchar(64)	邮箱
create_time	datetime	创建时间
2. 接口参数
注册/登录
POST /api/auth/register
username, password, phone, email
POST /api/auth/login
username, password
用户管理
GET /api/users
page, size, username, status
POST /api/users
username, password, phone, email, vip_level, status
PUT /api/users/{id}
username, phone, email, vip_level, status
DELETE /api/users/{id}





3. 业务流程图

二、房间管理模块
1. 字段设计（room）
字段名	类型	说明
id	bigint	主键
name	varchar(64)	房间名称
type	varchar(32)	房间类型
parent_id	bigint	父级房间ID
user_id	bigint	所属用户
create_time	datetime	创建时间
2. 接口参数
GET /api/rooms
userId, parentId, name
POST /api/rooms
name, type, parent_id, user_id
PUT /api/rooms/{id}
name, type, parent_id
DELETE /api/rooms/{id}
3. 业务流程图


三、设备管理模块
1. 字段设计（device）
字段名	类型	说明
id	bigint	主键
name	varchar(64)	设备名称
type	varchar(32)	设备类型
room_id	bigint	所属房间
status	tinyint	状态
mqtt_topic	varchar(128)	MQTT主题
last_online	datetime	最后在线时间
create_time	datetime	创建时间
2. 接口参数
GET /api/devices
roomId, type, status, name
POST /api/devices
name, type, room_id, mqtt_topic
PUT /api/devices/{id}
name, type, room_id, mqtt_topic, status
DELETE /api/devices/{id}
GET /api/devices/{id}/status
POST /api/devices/{id}/command
command, params

3. 业务流程图

四、场景管理模块
1. 字段设计（scene）
字段名	类型	说明
id	bigint	主键
name	varchar(64)	场景名称
user_id	bigint	所属用户
description	varchar(255)	描述
create_time	datetime	创建时间
2. 接口参数
GET /api/scenes
userId, name
POST /api/scenes
name, user_id, description
PUT /api/scenes/{id}
name, description
DELETE /api/scenes/{id}
3. 业务流程图

五、条件管理模块
1. 字段设计（condition）
字段名	类型	说明
id	bigint	主键
scene_id	bigint	所属场景
type	varchar(32)	条件类型
param	varchar(128)	条件参数
value	varchar(128)	条件值
create_time	datetime	创建时间
2. 接口参数
GET /api/conditions?sceneId=xxx
POST /api/conditions
scene_id, type, param, value
PUT /api/conditions/{id}
type, param, value
DELETE /api/conditions/{id}
3. 业务流程图
六、动作管理模块
1. 字段设计（action）
字段名	类型	说明
id	bigint	主键
scene_id	bigint	所属场景
device_id	bigint	设备ID
action_type	varchar(32)	动作类型
param	varchar(128)	动作参数
value	varchar(128)	动作值
create_time	datetime	创建时间
2. 接口参数
GET /api/actions?sceneId=xxx
POST /api/actions
scene_id, device_id, action_type, param, value
PUT /api/actions/{id}
device_id, action_type, param, value
DELETE /api/actions/{id}
3. 业务流程图

七、闹钟管理模块
1. 字段设计（alarm）
字段名	类型	说明
id	bigint	主键
device_id	bigint	设备ID
time	time	闹钟时间
repeat	varchar(32)	重复周期
status	tinyint	状态
create_time	datetime	创建时间
2. 接口参数
GET /api/alarms?deviceId=xxx
POST /api/alarms
device_id, time, repeat, status
PUT /api/alarms/{id}
time, repeat, status
DELETE /api/alarms/{id}
3. 业务流程图

八、红外码管理模块
1. 字段设计（infrared_code）
字段名	类型	说明
id	bigint	主键
device_id	bigint	设备ID
code	varchar(128)	红外码
description	varchar(255)	描述
create_time	datetime	创建时间

2. 接口参数
GET /api/infrared-codes?deviceId=xxx
POST /api/infrared-codes
device_id, code, description
PUT /api/infrared-codes/{id}
code, description
DELETE /api/infrared-codes/{id}
3. 业务流程图

九、充值/消费记录模块
1. 字段设计（transaction）
字段名	类型	说明
id	bigint	主键
user_id	bigint	用户ID
type	varchar(16)	类型
amount	decimal(10,2)	金额
description	varchar(255)	描述
create_time	datetime	创建时间
2. 接口参数
GET /api/transactions?userId=xxx
POST /api/transactions
user_id, type, amount, description
3. 业务流程图

十、设备数据管理（TDengine）
1. 字段设计（超级表 sensor_data）
字段名	类型	说明
ts	timestamp	时间戳
value	double	数据值
device_id	binary(32)	设备ID（tag）
room_id	binary(32)	房间ID（tag）
type	binary(32)	数据类型（tag）
2. 接口参数
GET /api/device-data?deviceId=xxx&type=temperature&start=xxx&end=xxx
2.业务流程图


四、MQTT 设计
设备与平台通过 MQTT Broker 通信。
主题设计建议：
设备上报：iot/device/{deviceId}/up
平台下发：iot/device/{deviceId}/down
消息内容采用 JSON 格式，包含设备ID、时间戳、数据类型、数据值等。
示例：
json
Apply to 项目设计书.md

{
  "deviceId": "123456",
  "timestamp": 1710000000,
  "type": "temperature",
  "value": 23.5
}

五、TDengine 设计（时序数据）
用于存储设备的实时传感器数据（如温湿度、电量等）。
每个设备可建超级表（Super Table），tag 为 deviceId、roomId、type 等。
超级表结构示例：
sql
Apply to 项目设计书.md

CREATE STABLE sensor_data (
  ts TIMESTAMP,
  value DOUBLE
) TAGS (
  device_id BINARY(32),
  room_id BINARY(32),
  type BINARY(32)
);
插入数据示例：
sql
Apply to 项目设计书.md

INSERT INTO d123456 USING sensor_data TAGS('123456', '101', 'temperature') VALUES (NOW, 23.5);

六、技术实现要点
设备权限、分组、联动等需细化设计。
设备数据采集、控制通过 MQTT 实现，实时数据入 TDengine，业务数据入 MySQL。
设备状态、场景联动、条件触发等需有定时/事件驱动机制。
用户管理、权限、VIP、充值消费等可复用 ruoyi-vue-pro 用户体系。
安全：OAuth2.0 + JWT，接口鉴权。



















一、ER图（文字描述）
1. 实体及主要字段
用户（User）
id（主键）
username（用户名）
password（密码）
vip_level（VIP等级）
phone（手机号）
email（邮箱）
status（状态）
create_time（创建时间）
房间（Room）
id（主键）
name（房间名称）
type（房间类型）
parent_id（父级房间ID，支持分组/树结构）
user_id（所属用户）
create_time（创建时间）
设备（Device）
id（主键）
name（设备名称）
type（设备类型）
room_id（所属房间）
status（状态）
mqtt_topic（MQTT主题）
last_online（最后在线时间）
create_time（创建时间）
设备操作记录（DeviceOperationLog）
id（主键）
device_id（设备ID）
user_id（操作人）
operation（操作类型）
content（操作内容）
create_time（操作时间）
场景（Scene）
id（主键）
name（场景名称）
user_id（所属用户）
description（描述）
mode（执行模式：手动/自动/他因/自定义）
create_time（创建时间）
条件（Condition）
id（主键）
scene_id（所属场景）
type（条件类型：传感器/电力等）
param（条件参数，如温度/湿度/光照等）
value（条件值）
operator（比较符号，如>、<、=）
create_time（创建时间）
动作（Action）
id（主键）
scene_id（所属场景）
device_id（设备ID）
action_type（动作类型：开关/调节等）
param（动作参数）
value（动作值）
create_time（创建时间）
闹钟（Alarm）
id（主键）
device_id（设备ID）
time（闹钟时间）
repeat（重复周期）
status（状态）
create_time（创建时间）
红外码（InfraredCode）
id（主键）
device_id（设备ID）
code（红外码）
description（描述）
create_time（创建时间）
充值/消费记录（Transaction）
id（主键）
user_id（用户ID）
type（充值/消费）
amount（金額）
description（描述）
create_time（创建时间）
设备时序数据（DeviceData，TDengine存储）
ts（时间戳）
value（数据值）
device_id（设备ID，tag）
room_id（房间ID，tag）
type（数据类型，tag）

2. 实体关系
用户（User）1对多 房间（Room）
房间（Room）1对多 设备（Device）
设备（Device）1对多 设备操作记录（DeviceOperationLog）
用户（User）1对多 场景（Scene）
场景（Scene）1对多 条件（Condition）
场景（Scene）1对多 动作（Action）
设备（Device）1对多 闹钟（Alarm）
设备（Device）1对多 红外码（InfraredCode）
用户（User）1对多 充值/消费记录（Transaction）
设备（Device）1对多 设备时序数据（DeviceData）

二、模块设计书
1. 房间子系统
1.1 房间管理（分组）
功能：支持用户自定义房间（如办公室、教室、宿舍等），支持分组/树形结构。
主要接口：
查询房间列表
新增/编辑/删除房间
核心字段：id, name, type, parent_id, user_id
业务要点：房间分组、树结构递归、用户隔离

2. 设备子系统
2.1 设备管理
功能：设备增删改查、状态监控、分组、权限、远程控制、告警、数据采集
主要接口：
查询设备列表
新增/编辑/删除设备
查询/下发设备状态
设备操作记录
核心字段：id, name, type, room_id, status, mqtt_topic, last_online
业务要点：MQTT通信、设备上下线、权限控制、设备分组、设备与房间/用户关联
2.2 设备操作记录
功能：记录设备的所有操作（如开关、调节、异常等）
主要接口：
查询操作记录
核心字段：device_id, user_id, operation, content, create_time

3. 场景子系统
3.1 场景管理
功能：场景定义、执行模式（手动/自动/他因/自定义）、场景联动
主要接口：
查询/新增/编辑/删除场景
核心字段：id, name, user_id, description, mode
3.2 条件管理
功能：配置场景触发条件（如温度>30℃、光照<100lux等），支持多条件组合
主要接口：
查询/新增/编辑/删除条件
核心字段：scene_id, type, param, value, operator
3.3 动作管理
功能：配置场景触发后的动作（如开空调、关灯等），支持多动作联动
主要接口：
查询/新增/编辑/删除动作
核心字段：scene_id, device_id, action_type, param, value

4. 内置闹钟子系统
4.1 闹钟管理
功能：定时任务、周期任务、设备定时控制
主要接口：
查询/新增/编辑/删除闹钟
核心字段：device_id, time, repeat, status

5. 红外功能子系统
5.1 红外码管理
功能：红外码学习、下发、管理
主要接口：
查询/新增/编辑/删除红外码
核心字段：device_id, code, description

6. 用户子系统
6.1 用户管理（VIP）
功能：用户注册、登录、VIP等级、权限、充值消费
主要接口：
用户注册/登录
查询/新增/编辑/删除用户
充值/消费
核心字段：id, username, password, vip_level, phone, email, status

7. 运营子系统
7.1 收费管理
功能：用户充值、消费、余额管理
主要接口：
查询/新增充值消费记录
核心字段：user_id, type, amount, description
7.2 账务导出
功能：导出用户账务明细
主要接口：
导出账务记录

8. 设备时序数据子系统（TDengine）
8.1 设备数据管理
功能：采集、存储、查询设备的时序数据（如温湿度、电量等）
主要接口：
查询设备时序数据
核心字段：ts, value, device_id, room_id, type

三、业务流程举例
1. 场景联动流程
1.用户配置场景（如“离家模式”），设置条件（如门磁关闭、无人感应），设置动作（如关灯、关空调）。
1.设备上报数据（如门磁状态、人体感应）到平台。
1.平台检测条件是否满足，若满足则触发动作。
1.平台通过MQTT下发指令到相关设备，执行动作。
1.设备执行后上报状态，平台记录操作日志。
2. 设备数据采集流程
1.设备定时/实时通过MQTT上报传感器数据（如温度、湿度）。
1.平台接收数据，写入TDengine（时序库）。
1.用户可通过前端查询设备历史/实时数据。

四、ER图关系总结（简明版）
用户（User）1对多 房间（Room）
房间（Room）1对多 设备（Device）
设备（Device）1对多 设备操作记录（DeviceOperationLog）
用户（User）1对多 场景（Scene）
场景（Scene）1对多 条件（Condition）
场景（Scene）1对多 动作（Action）
设备（Device）1对多 闹钟（Alarm）
设备（Device）1对多 红外码（InfraredCode）
用户（User）1对多 充值/消费记录（Transaction）
设备（Device）1对多 设备时序数据（DeviceData）
