/*
 * Copyright 2023-2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.iocoder.yudao.module.ai.framework.ai.core.model.siliconflow;

/**
 * SiliconFlow API 枚举类
 *
 * <AUTHOR>
 */
public final class SiliconFlowApiConstants {

	public static final String DEFAULT_BASE_URL = "https://api.siliconflow.cn";

	public static final String MODEL_DEFAULT = "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B";

    public static final String DEFAULT_IMAGE_MODEL = "Kwai-Kolors/Kolors";

	public static final String PROVIDER_NAME = "Siiconflow";

}
