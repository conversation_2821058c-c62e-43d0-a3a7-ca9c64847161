package cn.iocoder.yudao.module.iot.gateway.service.scene;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.iot.core.biz.IotSceneCommonApi;
import cn.iocoder.yudao.module.iot.core.biz.dto.IotSceneAuthReqDTO;
import cn.iocoder.yudao.module.iot.core.biz.dto.IotSceneGetReqDTO;
import cn.iocoder.yudao.module.iot.core.biz.dto.IotSceneRespDTO;
import cn.iocoder.yudao.module.iot.gateway.config.IotGatewayProperties;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * IoT 场景 API 实现类：通过 HTTP 调用远程的场景接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class IotSceneApiImpl implements IotSceneCommonApi {

    @Resource
    private IotGatewayProperties gatewayProperties;

    private RestTemplate restTemplate;

    @PostConstruct
    public void init() {
        IotGatewayProperties.RpcProperties rpc = gatewayProperties.getRpc();
        if (rpc != null && rpc.getUrl() != null) {
            restTemplate = new RestTemplateBuilder()
                    .rootUri(rpc.getUrl() + "/rpc-api/iot/scene")
                    .readTimeout(rpc.getReadTimeout())
                    .connectTimeout(rpc.getConnectTimeout())
                    .build();
        }
    }

    @Override
    public CommonResult<Boolean> authScene(IotSceneAuthReqDTO authReqDTO) {
        if (restTemplate == null) {
            log.warn("[authScene] RestTemplate 未初始化，返回默认成功");
            return success(true);
        }
        return doPost("/auth", authReqDTO, new ParameterizedTypeReference<>() {});
    }

    @Override
    public CommonResult<IotSceneRespDTO> getScene(IotSceneGetReqDTO infoReqDTO) {
        if (restTemplate == null) {
            log.warn("[getScene] RestTemplate 未初始化，返回空结果");
            return success(null);
        }
        return doPost("/get", infoReqDTO, new ParameterizedTypeReference<>() {});
    }

    @Override
    public CommonResult<List<IotSceneRespDTO>> getAllScenes(Long userId) {
        if (restTemplate == null) {
            log.warn("[getAllScenes] RestTemplate 未初始化，返回空列表");
            return success(Collections.emptyList());
        }
        try {
            String url = "/all" + (userId != null ? "?userId=" + userId : "");
            ResponseEntity<CommonResult<List<IotSceneRespDTO>>> response = restTemplate.exchange(
                    url, HttpMethod.GET, null, new ParameterizedTypeReference<>() {});
            CommonResult<List<IotSceneRespDTO>> result = response.getBody();
            Assert.notNull(result, "请求结果不能为空");
            return result;
        } catch (Exception e) {
            log.error("[getAllScenes] 获取所有场景失败，userId: {}", userId, e);
            return success(Collections.emptyList());
        }
    }

    private <T, R> CommonResult<R> doPost(String url, T body,
                                          ParameterizedTypeReference<CommonResult<R>> responseType) {
        try {
            HttpEntity<T> requestEntity = new HttpEntity<>(body);
            ResponseEntity<CommonResult<R>> response = restTemplate.exchange(
                    url, HttpMethod.POST, requestEntity, responseType);
            CommonResult<R> result = response.getBody();
            Assert.notNull(result, "请求结果不能为空");
            return result;
        } catch (Exception e) {
            log.error("[doPost] HTTP 请求失败，url: {}, body: {}", url, body, e);
            return CommonResult.error(INTERNAL_SERVER_ERROR);
        }
    }
}
