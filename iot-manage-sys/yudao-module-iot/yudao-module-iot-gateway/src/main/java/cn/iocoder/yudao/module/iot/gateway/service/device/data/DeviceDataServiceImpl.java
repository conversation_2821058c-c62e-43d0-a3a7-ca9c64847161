package cn.iocoder.yudao.module.iot.gateway.service.device.data;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 设备数据服务实现类
 */
@Service
@Slf4j
public class DeviceDataServiceImpl implements DeviceDataService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final String DEVICE_DATA_KEY_PREFIX = "iot:device:data:";
    private static final String DEVICE_PROPERTIES_KEY_PREFIX = "iot:device:properties:";
    private static final String DEVICE_SENSORS_KEY_PREFIX = "iot:device:sensors:";
    private static final int CACHE_EXPIRE_HOURS = 24;

    @Override
    public Map<String, Object> getDeviceData(Long deviceId) {
        try {
            String key = DEVICE_DATA_KEY_PREFIX + deviceId;
            String dataJson = stringRedisTemplate.opsForValue().get(key);
            
            if (StrUtil.isNotBlank(dataJson)) {
                return JSONUtil.toBean(dataJson, Map.class);
            }
            
            // 如果没有缓存数据，返回默认数据
            return createDefaultDeviceData(deviceId);
        } catch (Exception e) {
            log.error("获取设备数据失败，deviceId: {}", deviceId, e);
            return createDefaultDeviceData(deviceId);
        }
    }

    @Override
    public Map<String, Object> getDeviceProperties(Long deviceId) {
        try {
            String key = DEVICE_PROPERTIES_KEY_PREFIX + deviceId;
            String propertiesJson = stringRedisTemplate.opsForValue().get(key);
            
            if (StrUtil.isNotBlank(propertiesJson)) {
                return JSONUtil.toBean(propertiesJson, Map.class);
            }
            
            // 如果没有缓存数据，返回默认属性
            return createDefaultDeviceProperties(deviceId);
        } catch (Exception e) {
            log.error("获取设备属性失败，deviceId: {}", deviceId, e);
            return createDefaultDeviceProperties(deviceId);
        }
    }

    @Override
    public void updateDeviceData(Long deviceId, Map<String, Object> data) {
        try {
            String key = DEVICE_DATA_KEY_PREFIX + deviceId;
            String dataJson = JSONUtil.toJsonStr(data);
            stringRedisTemplate.opsForValue().set(key, dataJson, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            log.info("更新设备数据成功，deviceId: {}, data: {}", deviceId, dataJson);
        } catch (Exception e) {
            log.error("更新设备数据失败，deviceId: {}", deviceId, e);
        }
    }

    @Override
    public Map<String, Object> getDeviceSensors(Long deviceId) {
        try {
            String key = DEVICE_SENSORS_KEY_PREFIX + deviceId;
            String sensorsJson = stringRedisTemplate.opsForValue().get(key);
            
            if (StrUtil.isNotBlank(sensorsJson)) {
                return JSONUtil.toBean(sensorsJson, Map.class);
            }
            
            // 如果没有缓存数据，返回默认传感器数据
            return createDefaultDeviceSensors(deviceId);
        } catch (Exception e) {
            log.error("获取设备传感器数据失败，deviceId: {}", deviceId, e);
            return createDefaultDeviceSensors(deviceId);
        }
    }

    /**
     * 创建默认设备数据
     */
    private Map<String, Object> createDefaultDeviceData(Long deviceId) {
        Map<String, Object> data = new HashMap<>();
        data.put("deviceId", deviceId);
        data.put("online", true);
        data.put("lastUpdateTime", System.currentTimeMillis());
        data.put("temperature", 25.0);
        data.put("humidity", 60.0);
        data.put("brightness", 500);
        data.put("status", "normal");
        return data;
    }

    /**
     * 创建默认设备属性
     */
    private Map<String, Object> createDefaultDeviceProperties(Long deviceId) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("deviceId", deviceId);
        properties.put("deviceName", "设备" + deviceId);
        properties.put("deviceType", "sensor");
        properties.put("firmwareVersion", "v1.0.0");
        properties.put("ipAddress", "*************");
        properties.put("macAddress", "00:11:22:33:44:55");
        return properties;
    }

    /**
     * 创建默认设备传感器数据
     */
    private Map<String, Object> createDefaultDeviceSensors(Long deviceId) {
        Map<String, Object> sensors = new HashMap<>();
        sensors.put("temperature", Map.of(
            "value", 25.0,
            "unit", "°C",
            "type", "temperature",
            "status", "normal"
        ));
        sensors.put("humidity", Map.of(
            "value", 60.0,
            "unit", "%",
            "type", "humidity",
            "status", "normal"
        ));
        sensors.put("brightness", Map.of(
            "value", 500,
            "unit", "lux",
            "type", "brightness",
            "status", "normal"
        ));
        return sensors;
    }
} 