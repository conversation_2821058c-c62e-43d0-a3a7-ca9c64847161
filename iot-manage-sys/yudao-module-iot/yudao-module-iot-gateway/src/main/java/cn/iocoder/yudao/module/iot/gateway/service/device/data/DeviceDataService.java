package cn.iocoder.yudao.module.iot.gateway.service.device.data;

import java.util.Map;

/**
 * 设备数据服务接口
 */
public interface DeviceDataService {

    /**
     * 获取设备实时数据
     * @param deviceId 设备ID
     * @return 设备数据
     */
    Map<String, Object> getDeviceData(Long deviceId);

    /**
     * 获取设备属性数据
     * @param deviceId 设备ID
     * @return 设备属性
     */
    Map<String, Object> getDeviceProperties(Long deviceId);

    /**
     * 更新设备数据
     * @param deviceId 设备ID
     * @param data 设备数据
     */
    void updateDeviceData(Long deviceId, Map<String, Object> data);

    /**
     * 获取设备传感器数据
     * @param deviceId 设备ID
     * @return 传感器数据
     */
    Map<String, Object> getDeviceSensors(Long deviceId);
} 