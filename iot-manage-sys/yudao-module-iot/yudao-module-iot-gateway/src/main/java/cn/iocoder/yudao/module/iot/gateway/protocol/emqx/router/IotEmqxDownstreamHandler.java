package cn.iocoder.yudao.module.iot.gateway.protocol.emqx.router;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.module.iot.core.biz.dto.IotDeviceRespDTO;
import cn.iocoder.yudao.module.iot.core.enums.IotDeviceMessageMethodEnum;
import cn.iocoder.yudao.module.iot.core.mq.message.IotDeviceMessage;
import cn.iocoder.yudao.module.iot.core.util.IotDeviceMessageUtils;
import cn.iocoder.yudao.module.iot.gateway.protocol.emqx.IotEmqxUpstreamProtocol;
import cn.iocoder.yudao.module.iot.gateway.service.device.IotDeviceService;
import cn.iocoder.yudao.module.iot.gateway.service.device.message.IotDeviceMessageService;
import cn.iocoder.yudao.module.iot.gateway.service.scene.IotSceneService;
import cn.iocoder.yudao.module.iot.core.biz.dto.IotSceneActionDTO;
import cn.iocoder.yudao.module.iot.core.biz.dto.IotSceneRespDTO;
import cn.iocoder.yudao.module.iot.gateway.util.IotMqttTopicUtils;
import lombok.extern.slf4j.Slf4j;
import cn.iocoder.yudao.module.iot.controller.app.websocket.IotWebSocketController;

/**
 * IoT 网关 EMQX 下行消息处理器
 * <p>
 * 从消息总线接收到下行消息，然后发布到 MQTT Broker，从而被设备所接收
 *
 * <AUTHOR>
 */
@Slf4j
public class IotEmqxDownstreamHandler {

    private final IotEmqxUpstreamProtocol protocol;

    private final IotDeviceService deviceService;

    private final IotDeviceMessageService deviceMessageService;

    private final IotSceneService sceneService;

    public IotEmqxDownstreamHandler(IotEmqxUpstreamProtocol protocol) {
        this.protocol = protocol;
        this.deviceService = SpringUtil.getBean(IotDeviceService.class);
        this.deviceMessageService = SpringUtil.getBean(IotDeviceMessageService.class);
        this.sceneService = SpringUtil.getBean(IotSceneService.class);
    }

    /**
     * 处理下行消息
     *
     * @param message 设备消息
     */
    public void handle(IotDeviceMessage message) {
        // 1. 获取设备信息
        IotDeviceRespDTO deviceInfo = deviceService.getDeviceFromCache(message.getDeviceId());
        if (deviceInfo == null) {
            log.error("[handle][设备信息({})不存在]", message.getDeviceId());
            return;
        }

        // 1.5 获取所有场景，筛选包含当前设备id的场景
        // 假设有一个方法sceneService.getAllScenes()，返回所有IotSceneRespDTO
        java.util.List<IotSceneRespDTO> allScenes = getAllScenes(); // 你需实现此方法
        for (IotSceneRespDTO scene : allScenes) {
            // 假设scene有getConditions()方法，返回List<IotSceneConditionDTO>，你需补充DTO和getter
            if (scene.getConditions() == null) continue;
            boolean match = false;
            for (var cond : scene.getConditions()) {
                // 只处理当前设备相关条件
                if (cond.getDeviceId() != null && cond.getDeviceId().equals(message.getDeviceId())) {
                    // 假设 message.params 是 Map<String, Object>
                    if (message.getParams() instanceof java.util.Map) {
                        java.util.Map params = (java.util.Map) message.getParams();
                        Object val = params.get(cond.getAttr());
                        if (val != null && compare(val.toString(), cond.getValue(), cond.getOperator())) {
                            match = true;
                        }
                    }
                }
            }
            if (match && scene.getActions() != null) {
                for (var action : scene.getActions()) {
                    sendActionToDevice(action);
                }
            }
        }

        // 2. 推送到WebSocket前端
        try {
            IotWebSocketController.pushDeviceData(message);
            log.info("[handle] 设备数据已推送到WebSocket，deviceId: {}", message.getDeviceId());
        } catch (Exception e) {
            log.error("[handle] 推送设备数据到WebSocket失败，deviceId: {}", message.getDeviceId(), e);
        }

        // 2.1 根据方法构建主题
        String topic = buildTopicByMethod(message, deviceInfo.getProductKey(), deviceInfo.getDeviceName());
        if (StrUtil.isBlank(topic)) {
            log.warn("[handle][未知的消息方法: {}]", message.getMethod());
            return;
        }
        // 2.2 构建载荷
        byte[] payload = deviceMessageService.encodeDeviceMessage(message, deviceInfo.getProductKey(), deviceInfo.getDeviceName());
        // 2.3 发布消息
        protocol.publishMessage(topic, payload);
    }

    /**
     * 获取所有场景（通过API调用）
     */
    private java.util.List<IotSceneRespDTO> getAllScenes() {
        try {
            // 通过API调用获取所有场景，userId可根据实际需求传null或指定
            cn.iocoder.yudao.framework.common.pojo.CommonResult<java.util.List<IotSceneRespDTO>> result =
                    (cn.iocoder.yudao.framework.common.pojo.CommonResult<java.util.List<IotSceneRespDTO>>) sceneService.getAllScenes(); // 这里假设sceneService已注入并有getAllScenes方法
            if (result != null && result.getData() != null) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("[getAllScenes] 获取所有场景失败", e);
        }
        return java.util.Collections.emptyList();
    }

    /**
     * 条件对比，支持操作符
     */
    private boolean compare(String actual, String expect, String operator) {
        if (operator == null || operator.equals("=")) return actual.equals(expect);
        try {
            double a = Double.parseDouble(actual);
            double b = Double.parseDouble(expect);
            switch (operator) {
                case ">": return a > b;
                case ">=": return a >= b;
                case "<": return a < b;
                case "<=": return a <= b;
                case "!=": return a != b;
            }
        } catch (Exception ignore) {}
        return false;
    }

    /**
     * 根据消息方法和回复状态构建主题
     *
     * @param message    设备消息
     * @param productKey 产品标识
     * @param deviceName 设备名称
     * @return 构建的主题，如果方法不支持返回 null
     */
    private String buildTopicByMethod(IotDeviceMessage message, String productKey, String deviceName) {
        // 1. 解析消息方法
        IotDeviceMessageMethodEnum methodEnum = IotDeviceMessageMethodEnum.of(message.getMethod());
        if (methodEnum == null) {
            log.warn("[buildTopicByMethod][未知的消息方法: {}]", message.getMethod());
            return null;
        }

        // 2. 根据消息方法和回复状态，构建 topic
        boolean isReply = IotDeviceMessageUtils.isReplyMessage(message);

        // TODO @芋艿：需要添加对应的 Topic，所以需要先判断消息方法类型
        // TODO @haohao：基于 method，然后逆推对应的 topic，可以哇？约定好~
        // 根据消息方法和回复状态构建对应的主题
        switch (methodEnum) {
            case PROPERTY_POST:
                if (isReply) {
                    return IotMqttTopicUtils.buildPropertyPostReplyTopic(productKey, deviceName);
                }
                break;
            case PROPERTY_SET:
                if (!isReply) {
                    return IotMqttTopicUtils.buildPropertySetTopic(productKey, deviceName);
                }
                break;
        }

        log.warn("[buildTopicByMethod][暂时不支持的下行消息: method={}, isReply={}]",
                message.getMethod(), isReply);
        return null;
    }

    /**
     * 下发动作指令到设备（预留实现）
     */
    private void sendActionToDevice(IotSceneActionDTO action) {
        // TODO: 实际下发动作指令到设备，这里仅log
        log.info("[sendActionToDevice] 下发动作: {}", action);
    }
}