package cn.iocoder.yudao.module.iot.gateway.controller;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.iot.core.mq.message.IotDeviceMessage;
import cn.iocoder.yudao.module.iot.gateway.service.device.data.DeviceDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.stereotype.Controller;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 设备数据 WebSocket 控制器
 * 用于向前端推送设备实时数据
 */
@Controller
@EnableWebSocket
@Slf4j
public class DeviceDataController implements WebSocketConfigurer {

    @Resource
    private DeviceDataService deviceDataService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 存储所有连接的WebSocket会话
    private static final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new DeviceDataWebSocketHandler(), "/ws/device-data")
                .setAllowedOrigins("*");
    }

    /**
     * WebSocket处理器
     */
    public class DeviceDataWebSocketHandler extends TextWebSocketHandler {
        
        @Override
        public void afterConnectionEstablished(WebSocketSession session) throws Exception {
            String sessionId = session.getId();
            sessions.put(sessionId, session);
            log.info("WebSocket连接建立，sessionId: {}", sessionId);
        }

        @Override
        public void afterConnectionClosed(WebSocketSession session, org.springframework.web.socket.CloseStatus status) throws Exception {
            String sessionId = session.getId();
            sessions.remove(sessionId);
            log.info("WebSocket连接关闭，sessionId: {}", sessionId);
        }

        @Override
        protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
            // 处理前端发送的消息
            String payload = message.getPayload();
            log.info("收到WebSocket消息: {}", payload);
        }
    }

    /**
     * 推送设备数据到所有连接的客户端
     */
    public static void pushDeviceData(IotDeviceMessage deviceMessage) {
        try {
            String messageJson = new ObjectMapper().writeValueAsString(deviceMessage);
            TextMessage textMessage = new TextMessage(messageJson);
            
            for (WebSocketSession session : sessions.values()) {
                if (session.isOpen()) {
                    session.sendMessage(textMessage);
                }
            }
        } catch (Exception e) {
            log.error("推送设备数据失败", e);
        }
    }

    /**
     * 获取设备实时数据
     */
    @GetMapping("/api/device/data/{deviceId}")
    @ResponseBody
    public CommonResult<Map<String, Object>> getDeviceData(@PathVariable Long deviceId) {
        try {
            Map<String, Object> deviceData = deviceDataService.getDeviceData(deviceId);
            return CommonResult.success(deviceData);
        } catch (Exception e) {
            log.error("获取设备数据失败，deviceId: {}", deviceId, e);
            return CommonResult.error(500, "获取设备数据失败");
        }
    }

    /**
     * 获取设备属性数据
     */
    @GetMapping("/api/device/properties/{deviceId}")
    @ResponseBody
    public CommonResult<Map<String, Object>> getDeviceProperties(@PathVariable Long deviceId) {
        try {
            Map<String, Object> properties = deviceDataService.getDeviceProperties(deviceId);
            return CommonResult.success(properties);
        } catch (Exception e) {
            log.error("获取设备属性失败，deviceId: {}", deviceId, e);
            return CommonResult.error(500, "获取设备属性失败");
        }
    }
} 