package cn.iocoder.yudao.module.iot.service.room;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.iot.controller.admin.room.vo.IotRoomPageReqVO;
import cn.iocoder.yudao.module.iot.controller.admin.room.vo.IotRoomSaveReqVO;
import cn.iocoder.yudao.module.iot.dal.dataobject.room.IotRoomDO;
import cn.iocoder.yudao.module.iot.dal.mysql.room.IotRoomMapper;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import jakarta.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static cn.iocoder.yudao.module.iot.enums.ErrorCodeConstants.ROOM_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link IotRoomServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(IotRoomServiceImpl.class)
public class IotRoomServiceImplTest extends BaseDbUnitTest {

    @Resource
    private IotRoomServiceImpl roomService;

    @Resource
    private IotRoomMapper roomMapper;

    @Test
    public void testCreateRoom_success() {
        // 准备参数
        IotRoomSaveReqVO createReqVO = randomPojo(IotRoomSaveReqVO.class, o -> {
            o.setId(null);
            o.setUserId(1L);
            o.setName("测试房间");
        });

        // 调用
        Long roomId = roomService.createRoom(createReqVO);
        // 断言
        assertNotNull(roomId);
        // 校验记录的属性是否正确
        IotRoomDO room = roomMapper.selectById(roomId);
        assertPojoEquals(createReqVO, room, "id");
    }

    @Test
    public void testUpdateRoom_success() {
        // mock 数据
        IotRoomDO dbRoom = randomPojo(IotRoomDO.class, o -> {
            o.setUserId(1L);
            o.setName("原房间名");
        });
        roomMapper.insert(dbRoom);// @Sql: 先插入出一条存在的数据
        // 准备参数
        IotRoomSaveReqVO updateReqVO = randomPojo(IotRoomSaveReqVO.class, o -> {
            o.setId(dbRoom.getId()); // 设置更新的 ID
            o.setUserId(1L);
            o.setName("新房间名");
        });

        // 调用
        roomService.updateRoom(updateReqVO);
        // 校验是否更新正确
        IotRoomDO room = roomMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, room);
    }

    @Test
    public void testUpdateRoom_notExists() {
        // 准备参数
        IotRoomSaveReqVO updateReqVO = randomPojo(IotRoomSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> roomService.updateRoom(updateReqVO), ROOM_NOT_EXISTS);
    }

    @Test
    public void testDeleteRoom_success() {
        // mock 数据
        IotRoomDO dbRoom = randomPojo(IotRoomDO.class);
        roomMapper.insert(dbRoom);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbRoom.getId();

        // 调用
        roomService.deleteRoom(id);
        // 校验数据不存在了
        assertNull(roomMapper.selectById(id));
    }

    @Test
    public void testDeleteRoom_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> roomService.deleteRoom(id), ROOM_NOT_EXISTS);
    }

    @Test
    public void testGetRoomPage() {
        // mock 数据
        IotRoomDO dbRoom = randomPojo(IotRoomDO.class, o -> {
            o.setName("测试房间");
            o.setType("living_room");
            o.setUserId(1L);
        });
        roomMapper.insert(dbRoom);
        // 测试 name 不匹配
        roomMapper.insert(cloneIgnoreId(dbRoom, o -> o.setName("其他房间")));
        // 测试 type 不匹配
        roomMapper.insert(cloneIgnoreId(dbRoom, o -> o.setType("bedroom")));
        // 测试 userId 不匹配
        roomMapper.insert(cloneIgnoreId(dbRoom, o -> o.setUserId(2L)));
        // 准备参数
        IotRoomPageReqVO reqVO = new IotRoomPageReqVO();
        reqVO.setName("测试");
        reqVO.setType("living_room");
        reqVO.setUserId(1L);

        // 调用
        PageResult<IotRoomDO> pageResult = roomService.getRoomPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbRoom, pageResult.getList().get(0));
    }

    @Test
    public void testGetRoomListByUserId() {
        // mock 数据
        IotRoomDO dbRoom = randomPojo(IotRoomDO.class, o -> o.setUserId(1L));
        roomMapper.insert(dbRoom);
        // 测试 userId 不匹配
        roomMapper.insert(cloneIgnoreId(dbRoom, o -> o.setUserId(2L)));
        // 准备参数
        Long userId = 1L;

        // 调用
        List<IotRoomDO> list = roomService.getRoomListByUserId(userId);
        // 断言
        assertEquals(1, list.size());
        assertPojoEquals(dbRoom, list.get(0));
    }

    @Test
    public void testGetRoomListByParentId() {
        // mock 数据
        IotRoomDO dbRoom = randomPojo(IotRoomDO.class, o -> o.setParentId(1L));
        roomMapper.insert(dbRoom);
        // 测试 parentId 不匹配
        roomMapper.insert(cloneIgnoreId(dbRoom, o -> o.setParentId(2L)));
        // 准备参数
        Long parentId = 1L;

        // 调用
        List<IotRoomDO> list = roomService.getRoomListByParentId(parentId);
        // 断言
        assertEquals(1, list.size());
        assertPojoEquals(dbRoom, list.get(0));
    }

}
