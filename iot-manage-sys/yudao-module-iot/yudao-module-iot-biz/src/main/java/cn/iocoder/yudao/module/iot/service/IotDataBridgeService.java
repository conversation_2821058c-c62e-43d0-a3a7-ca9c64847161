package cn.iocoder.yudao.module.iot.service;

import cn.iocoder.yudao.module.iot.dal.dataobject.IotDataBridgeDO;
import java.util.List;

public interface IotDataBridgeService {
    Long createDataBridge(IotDataBridgeDO dataBridge);
    void updateDataBridge(IotDataBridgeDO dataBridge);
    void deleteDataBridge(Long id);
    IotDataBridgeDO getDataBridge(Long id);
    List<IotDataBridgeDO> getDataBridgeList();
} 