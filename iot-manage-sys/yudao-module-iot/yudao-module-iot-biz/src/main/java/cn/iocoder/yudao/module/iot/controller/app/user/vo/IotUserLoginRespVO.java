package cn.iocoder.yudao.module.iot.controller.app.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户 APP - IoT用户登录 Response VO")
@Data
public class IotUserLoginRespVO {

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "testuser")
    private String username;

    @Schema(description = "VIP等级", example = "0")
    private Integer vipLevel;

    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "登录令牌", requiredMode = Schema.RequiredMode.REQUIRED, example = "happy")
    private String token;

    @Schema(description = "登录时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime loginTime;
} 