package cn.iocoder.yudao.module.iot.controller.app.websocket;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.iot.core.mq.message.IotDeviceMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * IoT WebSocket 处理器
 * 用于向前端推送设备实时数据
 */
@Component
@Slf4j
public class IotWebSocketController extends TextWebSocketHandler {

    /**
     * 存储所有连接的WebSocket会话
     * key: sessionId, value: WebSocketSession
     */
    private static final Map<String, WebSocketSession> SESSIONS = new ConcurrentHashMap<>();

    /**
     * 存储用户订阅的设备
     * key: sessionId, value: 设备ID列表
     */
    private static final Map<String, java.util.Set<String>> USER_SUBSCRIPTIONS = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        SESSIONS.put(sessionId, session);
        USER_SUBSCRIPTIONS.put(sessionId, new java.util.concurrent.ConcurrentHashMap.newKeySet());
        log.info("[WebSocket] 客户端连接成功，sessionId: {}", sessionId);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String sessionId = session.getId();
        SESSIONS.remove(sessionId);
        USER_SUBSCRIPTIONS.remove(sessionId);
        log.info("[WebSocket] 客户端断开连接，sessionId: {}", sessionId);
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String sessionId = session.getId();
        String payload = message.getPayload();
        log.info("[WebSocket] 收到消息，sessionId: {}, payload: {}", sessionId, payload);

        try {
            // 解析消息
            java.util.Map<String, Object> data = JSONUtil.toBean(payload, java.util.Map.class);
            String type = (String) data.get("type");
            
            if ("subscribe".equals(type)) {
                // 订阅设备
                handleSubscribe(sessionId, data);
            } else if ("unsubscribe".equals(type)) {
                // 取消订阅
                handleUnsubscribe(sessionId, data);
            } else {
                // 发送错误响应
                sendErrorResponse(session, "未知的消息类型: " + type);
            }
        } catch (Exception e) {
            log.error("[WebSocket] 处理消息失败", e);
            sendErrorResponse(session, "消息格式错误");
        }
    }

    /**
     * 处理订阅请求
     */
    private void handleSubscribe(String sessionId, java.util.Map<String, Object> data) {
        java.util.Set<String> subscriptions = USER_SUBSCRIPTIONS.get(sessionId);
        if (subscriptions != null) {
            java.util.List<String> deviceIds = (java.util.List<String>) data.get("deviceIds");
            if (deviceIds != null) {
                subscriptions.addAll(deviceIds);
                log.info("[WebSocket] 用户订阅设备，sessionId: {}, deviceIds: {}", sessionId, deviceIds);
            }
        }
    }

    /**
     * 处理取消订阅请求
     */
    private void handleUnsubscribe(String sessionId, java.util.Map<String, Object> data) {
        java.util.Set<String> subscriptions = USER_SUBSCRIPTIONS.get(sessionId);
        if (subscriptions != null) {
            java.util.List<String> deviceIds = (java.util.List<String>) data.get("deviceIds");
            if (deviceIds != null) {
                subscriptions.removeAll(deviceIds);
                log.info("[WebSocket] 用户取消订阅设备，sessionId: {}, deviceIds: {}", sessionId, deviceIds);
            }
        }
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(WebSocketSession session, String error) {
        try {
            java.util.Map<String, Object> response = new java.util.HashMap<>();
            response.put("type", "error");
            response.put("message", error);
            session.sendMessage(new TextMessage(JSONUtil.toJsonStr(response)));
        } catch (IOException e) {
            log.error("[WebSocket] 发送错误响应失败", e);
        }
    }

    /**
     * 推送设备数据到前端
     * 由MQTT消息处理器调用
     */
    public static void pushDeviceData(IotDeviceMessage deviceMessage) {
        if (deviceMessage == null || StrUtil.isBlank(deviceMessage.getDeviceId())) {
            return;
        }

        // 构建推送数据
        java.util.Map<String, Object> pushData = new java.util.HashMap<>();
        pushData.put("type", "deviceData");
        pushData.put("deviceId", deviceMessage.getDeviceId());
        pushData.put("method", deviceMessage.getMethod());
        pushData.put("params", deviceMessage.getParams());
        pushData.put("timestamp", System.currentTimeMillis());

        String message = JSONUtil.toJsonStr(pushData);
        log.info("[WebSocket] 推送设备数据: {}", message);

        // 推送给所有订阅了该设备的用户
        for (java.util.Map.Entry<String, java.util.Set<String>> entry : USER_SUBSCRIPTIONS.entrySet()) {
            String sessionId = entry.getKey();
            java.util.Set<String> subscriptions = entry.getValue();
            
            if (subscriptions.contains(deviceMessage.getDeviceId())) {
                WebSocketSession session = SESSIONS.get(sessionId);
                if (session != null && session.isOpen()) {
                    try {
                        session.sendMessage(new TextMessage(message));
                        log.debug("[WebSocket] 推送成功，sessionId: {}, deviceId: {}", sessionId, deviceMessage.getDeviceId());
                    } catch (IOException e) {
                        log.error("[WebSocket] 推送失败，sessionId: {}", sessionId, e);
                        // 移除失效的会话
                        SESSIONS.remove(sessionId);
                        USER_SUBSCRIPTIONS.remove(sessionId);
                    }
                }
            }
        }
    }

    /**
     * 获取当前连接数
     */
    public static int getConnectionCount() {
        return SESSIONS.size();
    }
} 