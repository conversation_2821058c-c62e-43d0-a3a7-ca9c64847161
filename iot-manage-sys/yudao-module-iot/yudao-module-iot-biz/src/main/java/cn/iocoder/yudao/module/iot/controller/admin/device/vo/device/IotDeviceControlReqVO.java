package cn.iocoder.yudao.module.iot.controller.admin.device.vo.device;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class IotDeviceControlReqVO {
    @Schema(description = "设备ID", required = true)
    @NotNull(message = "设备ID不能为空")
    private Long deviceId;

    @Schema(description = "指令类型", example = "switch")
    private String commandType;

    @Schema(description = "指令参数，JSON字符串", example = "{\"on\":true}")
    private String commandParams;
} 