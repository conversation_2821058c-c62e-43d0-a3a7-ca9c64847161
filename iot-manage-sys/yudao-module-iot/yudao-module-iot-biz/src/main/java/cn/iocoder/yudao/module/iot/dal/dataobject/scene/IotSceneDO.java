package cn.iocoder.yudao.module.iot.dal.dataobject.scene;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 场景 DO
 *
 * <AUTHOR>
 */
@TableName("iot_scene")
@KeySequence("iot_scene_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IotSceneDO extends TenantBaseDO {

    /**
     * 场景ID
     */
    @TableId
    private Long id;
    /**
     * 场景名称
     */
    private String name;
    /**
     * 场景描述
     */
    private String description;
    /**
     * 状态 1启用 0禁用
     */
    private Integer status;
    /**
     * 创建用户ID
     */
    private Long userId;

} 