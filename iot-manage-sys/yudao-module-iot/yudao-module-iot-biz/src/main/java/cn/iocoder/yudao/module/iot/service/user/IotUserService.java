package cn.iocoder.yudao.module.iot.service.user;

import cn.iocoder.yudao.module.iot.controller.app.user.vo.IotUserLoginReqVO;
import cn.iocoder.yudao.module.iot.controller.app.user.vo.IotUserLoginRespVO;
import cn.iocoder.yudao.module.iot.controller.app.user.vo.IotUserRegisterReqVO;
import cn.iocoder.yudao.module.iot.controller.app.user.vo.IotUserRespVO;

/**
 * IoT用户 Service 接口
 */
public interface IotUserService {

    /**
     * 用户登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    IotUserLoginRespVO login(IotUserLoginReqVO reqVO);

    /**
     * 用户注册
     *
     * @param reqVO 注册信息
     */
    void register(IotUserRegisterReqVO reqVO);

    /**
     * 用户退出登录
     */
    void logout();

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    IotUserRespVO getCurrentUserInfo();

    /**
     * 更新用户信息
     *
     * @param reqVO 用户信息
     */
    void updateUserInfo(IotUserRespVO reqVO);
} 