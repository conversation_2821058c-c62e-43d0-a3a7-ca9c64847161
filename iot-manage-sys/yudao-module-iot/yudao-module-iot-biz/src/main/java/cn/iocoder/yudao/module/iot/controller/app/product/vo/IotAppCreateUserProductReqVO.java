package cn.iocoder.yudao.module.iot.controller.app.product.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import com.fasterxml.jackson.annotation.JsonProperty;

@Schema(description = "用户 APP - 创建用户产品 Request VO")
@Data
public class IotAppCreateUserProductReqVO {

    @Schema(description = "产品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "产品ID不能为空")
    private Long productId;

    @Schema(description = "房间ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "房间ID不能为空")
    private Long roomId;

    @Schema(description = "主机/副机", requiredMode = Schema.RequiredMode.REQUIRED, example = "Master")
    @NotNull(message = "设备类型不能为空")
    @JsonProperty("MasterSlave")
    private String masterSlave;

    @Schema(description = "设备名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "我的设备")
    @NotNull(message = "设备名称不能为空")
    private String deviceName;

} 