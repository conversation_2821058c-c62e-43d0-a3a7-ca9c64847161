package cn.iocoder.yudao.module.iot.service.scene;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.iot.controller.app.scene.vo.IotSceneActionCreateReqVO;
import cn.iocoder.yudao.module.iot.controller.app.scene.vo.IotSceneActionRespVO;
import cn.iocoder.yudao.module.iot.controller.app.scene.vo.IotSceneConditionCreateReqVO;
import cn.iocoder.yudao.module.iot.controller.app.scene.vo.IotSceneConditionRespVO;
import cn.iocoder.yudao.module.iot.controller.app.scene.vo.IotSceneCreateReqVO;
import cn.iocoder.yudao.module.iot.controller.app.scene.vo.IotSceneUpdateReqVO;
import cn.iocoder.yudao.module.iot.controller.app.scene.vo.IotSceneRespVO;
import cn.iocoder.yudao.module.iot.dal.dataobject.device.IotDeviceDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.scene.IotSceneActionDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.scene.IotSceneConditionDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.scene.IotSceneDO;
import cn.iocoder.yudao.module.iot.dal.dataobject.scene.IotSceneLogDO;
import cn.iocoder.yudao.module.iot.dal.mysql.device.IotDeviceMapper;
import cn.iocoder.yudao.module.iot.dal.mysql.scene.IotSceneActionMapper;
import cn.iocoder.yudao.module.iot.dal.mysql.scene.IotSceneConditionMapper;
import cn.iocoder.yudao.module.iot.dal.mysql.scene.IotSceneLogMapper;
import cn.iocoder.yudao.module.iot.dal.mysql.scene.IotSceneMapper;
import cn.iocoder.yudao.module.iot.dal.mysql.product.IotProductMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.iot.enums.ErrorCodeConstants.SCENE_NOT_EXISTS;

/**
 * 场景 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class IotSceneServiceImpl implements IotSceneService {

    @Resource
    private IotSceneMapper sceneMapper;
    @Resource
    private IotSceneConditionMapper sceneConditionMapper;
    @Resource
    private IotSceneActionMapper sceneActionMapper;
    @Resource
    private IotSceneLogMapper sceneLogMapper;
    @Resource
    private IotDeviceMapper deviceMapper;
    @Resource
    private cn.iocoder.yudao.module.iot.dal.mysql.product.IotProductMapper productMapper;
    @Resource
    private cn.iocoder.yudao.module.iot.dal.mysql.product.IotUserProductMapper userProductMapper;

    @Override
    public Long createScene(IotSceneCreateReqVO createReqVO, Long userId) {
        // 插入场景
        IotSceneDO scene = BeanUtil.copyProperties(createReqVO, IotSceneDO.class);
        scene.setUserId(userId);
        sceneMapper.insert(scene);

        // 插入条件
        if (createReqVO.getConditions() != null) {
            for (IotSceneConditionCreateReqVO conditionReqVO : createReqVO.getConditions()) {
                IotSceneConditionDO condition = BeanUtil.copyProperties(conditionReqVO, IotSceneConditionDO.class);
                condition.setSceneId(scene.getId());
                // 手动set，防止字段名不一致
                condition.setProductId(conditionReqVO.getProductId());
                condition.setDeviceId(conditionReqVO.getDeviceId());
                condition.setAttr(conditionReqVO.getAttr());
                condition.setValue(conditionReqVO.getValue());
                sceneConditionMapper.insert(condition);
            }
        }

        // 插入动作
        if (createReqVO.getActions() != null) {
            for (int i = 0; i < createReqVO.getActions().size(); i++) {
                IotSceneActionCreateReqVO actionReqVO = createReqVO.getActions().get(i);
                IotSceneActionDO action = BeanUtil.copyProperties(actionReqVO, IotSceneActionDO.class);
                action.setSceneId(scene.getId());
                // 手动set，防止字段名不一致
                action.setProductId(actionReqVO.getProductId());
                action.setDeviceId(actionReqVO.getDeviceId());
                action.setAttr(actionReqVO.getAttr());
                action.setValue(actionReqVO.getValue());
                action.setActionType(actionReqVO.getActionType());
                action.setActionValue(actionReqVO.getActionValue());
                if (action.getOrderNum() == null) {
                    action.setOrderNum(i + 1);
                }
                sceneActionMapper.insert(action);
            }
        }

        // 发送MQTT消息通知设备
        sendSceneUpdateMessage(scene.getId());

        return scene.getId();
    }

    @Override
    public void updateScene(IotSceneUpdateReqVO updateReqVO, Long userId) {
        // 校验场景存在
        IotSceneDO existingScene = validateSceneExists(updateReqVO.getId());

        // 校验用户权限
        if (existingScene.getUserId() != null && !existingScene.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此场景");
        }

        // 更新场景基本信息
        IotSceneDO scene = BeanUtil.copyProperties(updateReqVO, IotSceneDO.class);
        scene.setUserId(userId);
        sceneMapper.updateById(scene);

        // 删除原有条件和动作
        sceneConditionMapper.deleteBySceneId(updateReqVO.getId());
        sceneActionMapper.deleteBySceneId(updateReqVO.getId());

        // 插入新的条件
        if (updateReqVO.getConditions() != null) {
            for (int i = 0; i < updateReqVO.getConditions().size(); i++) {
                IotSceneConditionCreateReqVO conditionReqVO = updateReqVO.getConditions().get(i);
                IotSceneConditionDO condition = BeanUtil.copyProperties(conditionReqVO, IotSceneConditionDO.class);
                condition.setSceneId(updateReqVO.getId());
                if (condition.getPriority() == null) {
                    condition.setPriority(i + 1);
                }
                sceneConditionMapper.insert(condition);
            }
        }

        // 插入新的动作
        if (updateReqVO.getActions() != null) {
            for (int i = 0; i < updateReqVO.getActions().size(); i++) {
                IotSceneActionCreateReqVO actionReqVO = updateReqVO.getActions().get(i);
                IotSceneActionDO action = BeanUtil.copyProperties(actionReqVO, IotSceneActionDO.class);
                action.setSceneId(updateReqVO.getId());
                if (action.getOrderNum() == null) {
                    action.setOrderNum(i + 1);
                }
                sceneActionMapper.insert(action);
            }
        }

        // 发送MQTT消息通知设备
        sendSceneUpdateMessage(updateReqVO.getId());

        log.info("更新场景成功，场景ID：{}，用户ID：{}", updateReqVO.getId(), userId);
    }

    @Override
    public void deleteScene(Long id) {
        // 校验存在
        validateSceneExists(id);

        // 删除场景
        sceneMapper.deleteById(id);
        // 删除条件
        sceneConditionMapper.deleteBySceneId(id);
        // 删除动作
        sceneActionMapper.deleteBySceneId(id);

        // 发送MQTT消息通知设备
        sendSceneDeleteMessage(id);
    }

    @Override
    public IotSceneDO getScene(Long id) {
        return sceneMapper.selectById(id);
    }

    @Override
    public IotSceneRespVO getSceneDetail(Long id) {
        // 校验存在
        IotSceneDO scene = validateSceneExists(id);

        // 获取条件列表
        List<IotSceneConditionDO> conditions = sceneConditionMapper.selectListBySceneId(id);
        List<IotSceneConditionRespVO> conditionRespVOs = BeanUtil.copyToList(conditions, IotSceneConditionRespVO.class);
        for (int i = 0; i < conditions.size(); i++) {
            IotSceneConditionDO c = conditions.get(i);
            IotSceneConditionRespVO v = conditionRespVOs.get(i);
            v.setProductId(c.getProductId());
            v.setDeviceId(c.getDeviceId());
            v.setAttr(c.getAttr());
            v.setValue(c.getValue());
            v.setOperator(c.getOperator());
        }

        // 获取动作列表
        List<IotSceneActionDO> actions = sceneActionMapper.selectListBySceneId(id);
        List<IotSceneActionRespVO> actionRespVOs = BeanUtil.copyToList(actions, IotSceneActionRespVO.class);
        for (int i = 0; i < actions.size(); i++) {
            IotSceneActionDO a = actions.get(i);
            IotSceneActionRespVO v = actionRespVOs.get(i);
            v.setProductId(a.getProductId());
            v.setDeviceId(a.getDeviceId());
            v.setAttr(a.getAttr());
            v.setValue(a.getValue());
        }

        // 批量查设备名称
        List<Long> deviceIds = conditionRespVOs.stream()
            .map(IotSceneConditionRespVO::getDeviceId)
            .collect(Collectors.toList());
        deviceIds.addAll(actionRespVOs.stream()
            .map(IotSceneActionRespVO::getDeviceId)
            .collect(Collectors.toList()));
        deviceIds = deviceIds.stream().filter(id2 -> id2 != null).distinct().collect(Collectors.toList());
        Map<Long, String> deviceNameMap = deviceIds.isEmpty() ? Map.of() :
            deviceMapper.selectList(new cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX<cn.iocoder.yudao.module.iot.dal.dataobject.device.IotDeviceDO>()
                .in(cn.iocoder.yudao.module.iot.dal.dataobject.device.IotDeviceDO::getId, deviceIds))
                .stream().collect(Collectors.toMap(
                    cn.iocoder.yudao.module.iot.dal.dataobject.device.IotDeviceDO::getId,
                    cn.iocoder.yudao.module.iot.dal.dataobject.device.IotDeviceDO::getDeviceName,
                    (v1, v2) -> v1));
        // 批量查产品名称（优先iot_user_product.product_name）
        List<Long> productIds = conditionRespVOs.stream()
            .map(IotSceneConditionRespVO::getProductId)
            .collect(Collectors.toList());
        productIds.addAll(actionRespVOs.stream()
            .map(IotSceneActionRespVO::getProductId)
            .collect(Collectors.toList()));
        productIds = productIds.stream().filter(id2 -> id2 != null).distinct().collect(Collectors.toList());
        // 1. 查iot_user_product表，直接用@Resource注入的userProductMapper
        List<cn.iocoder.yudao.module.iot.dal.dataobject.product.IotUserProductDO> userProducts =
            userProductMapper.selectListByProductIds(productIds);
        Map<Long, String> userProductNameMap = userProducts.stream().collect(Collectors.toMap(
            cn.iocoder.yudao.module.iot.dal.dataobject.product.IotUserProductDO::getProductId,
            cn.iocoder.yudao.module.iot.dal.dataobject.product.IotUserProductDO::getProductName,
            (v1, v2) -> v1));
        // 2. 查iot_product表
        Map<Long, String> productNameMap = productIds.isEmpty() ? Map.of() :
            productMapper.selectList(new cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX<cn.iocoder.yudao.module.iot.dal.dataobject.product.IotProductDO>()
                .in(cn.iocoder.yudao.module.iot.dal.dataobject.product.IotProductDO::getId, productIds))
                .stream().collect(Collectors.toMap(
                    p -> p.getId(),
                    p -> p.getName(),
                    (v1, v2) -> v1));
        // set 名称到VO，优先userProductNameMap
        conditionRespVOs.forEach(cond -> {
            cond.setDeviceName(deviceNameMap.get(cond.getDeviceId()));
            cond.setProductName(userProductNameMap.getOrDefault(cond.getProductId(), productNameMap.get(cond.getProductId())));
        });
        actionRespVOs.forEach(act -> {
            act.setDeviceName(deviceNameMap.get(act.getDeviceId()));
            act.setProductName(userProductNameMap.getOrDefault(act.getProductId(), productNameMap.get(act.getProductId())));
        });

        // 调试输出
        System.out.println("条件VO: " + cn.hutool.json.JSONUtil.toJsonStr(conditionRespVOs));
        System.out.println("动作VO: " + cn.hutool.json.JSONUtil.toJsonStr(actionRespVOs));

        // 组装返回
        IotSceneRespVO respVO = BeanUtil.copyProperties(scene, IotSceneRespVO.class);
        respVO.setConditions(conditionRespVOs);
        respVO.setActions(actionRespVOs);
        return respVO;
    }

    @Override
    public List<IotSceneRespVO> getSceneList(Long userId) {
        List<IotSceneDO> scenes = sceneMapper.selectListByUserId(userId);
        return scenes.stream().map(scene -> {
            IotSceneRespVO respVO = BeanUtil.copyProperties(scene, IotSceneRespVO.class);
            // 获取动作数量
            List<IotSceneActionDO> actions = sceneActionMapper.selectListBySceneId(scene.getId());
            respVO.setActions(BeanUtil.copyToList(actions, IotSceneActionRespVO.class));
            return respVO;
        }).collect(Collectors.toList());
    }

    @Override
    public void executeScene(Long id, Long userId) {
        // 校验存在
        IotSceneDO scene = validateSceneExists(id);

        long startTime = System.currentTimeMillis();
        String executeResult = "SUCCESS";
        String errorMessage = null;

        try {
            // 获取场景动作
            List<IotSceneActionDO> actions = sceneActionMapper.selectListBySceneId(id);
            
            // 执行动作
            for (IotSceneActionDO action : actions) {
                try {
                    executeAction(action);
                    // 延迟执行
                    if (action.getDelay() != null && action.getDelay() > 0) {
                        Thread.sleep(action.getDelay() * 1000L);
                    }
                } catch (Exception e) {
                    log.error("执行场景动作失败，场景ID：{}，动作ID：{}", id, action.getId(), e);
                    executeResult = "PARTIAL";
                    errorMessage = StrUtil.isBlank(errorMessage) ? e.getMessage() : errorMessage + ";" + e.getMessage();
                }
            }
        } catch (Exception e) {
            log.error("执行场景失败，场景ID：{}", id, e);
            executeResult = "FAILED";
            errorMessage = e.getMessage();
        }

        // 记录执行日志
        IotSceneLogDO log = IotSceneLogDO.builder()
                .sceneId(id)
                .sceneName(scene.getName())
                .triggerType("MANUAL")
                .triggerCondition("手动触发")
                .executeResult(executeResult)
                .executeTime(LocalDateTime.now())
                .duration((int) (System.currentTimeMillis() - startTime))
                .errorMessage(errorMessage)
                .userId(userId)
                .build();
        sceneLogMapper.insert(log);
    }

    @Override
    public java.util.List<cn.iocoder.yudao.module.iot.dal.dataobject.scene.IotSceneActionDO> getSceneActions(Long sceneId) {
        return sceneActionMapper.selectListBySceneId(sceneId);
    }

    private IotSceneDO validateSceneExists(Long id) {
        IotSceneDO scene = sceneMapper.selectById(id);
        if (scene == null) {
            throw exception(SCENE_NOT_EXISTS);
        }
        return scene;
    }

    private void executeAction(IotSceneActionDO action) {
        // 这里实现具体的设备控制逻辑
        // 可以通过MQTT发送指令到设备
        String topic = "iot/device/" + action.getDeviceId() + "/control";
        String payload = JSONUtil.toJsonStr(action);
        
        // TODO: 发送MQTT消息
        log.info("发送设备控制指令，设备ID：{}，动作：{}", action.getDeviceId(), action.getActionType());
    }

    private void sendSceneUpdateMessage(Long sceneId) {
        // TODO: 发送场景更新MQTT消息
        log.info("发送场景更新消息，场景ID：{}", sceneId);
    }

    private void sendSceneDeleteMessage(Long sceneId) {
        // TODO: 发送场景删除MQTT消息
        log.info("发送场景删除消息，场景ID：{}", sceneId);
    }

    @Override
    public void updateSceneStatus(Long id, Integer status, Long userId) {
        // 校验场景存在
        IotSceneDO scene = validateSceneExists(id);

        // 校验用户权限（可选：检查场景是否属于当前用户）
        if (scene.getUserId() != null && !scene.getUserId().equals(userId)) {
            throw new RuntimeException("无权限操作此场景");
        }

        // 更新状态
        IotSceneDO updateObj = new IotSceneDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        sceneMapper.updateById(updateObj);

        // 发送MQTT消息通知设备
        sendSceneUpdateMessage(id);

        log.info("更新场景状态成功，场景ID：{}，状态：{}，用户ID：{}", id, status, userId);
    }

}