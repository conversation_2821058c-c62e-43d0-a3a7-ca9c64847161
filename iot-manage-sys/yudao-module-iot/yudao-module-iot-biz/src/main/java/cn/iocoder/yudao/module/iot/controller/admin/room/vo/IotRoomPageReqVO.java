package cn.iocoder.yudao.module.iot.controller.admin.room.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - IoT 房间分页 Request VO")
@Data
public class IotRoomPageReqVO extends PageParam {

    @Schema(description = "房间名称", example = "客厅")
    private String name;

    @Schema(description = "房间类型", example = "living_room")
    private String type;

    @Schema(description = "父级房间ID", example = "1")
    private Long parentId;

    @Schema(description = "所属用户ID", example = "1")
    private Long userId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
