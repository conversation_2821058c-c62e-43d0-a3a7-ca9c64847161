package cn.iocoder.yudao.module.iot.dal.dataobject.rule.config;

import lombok.Data;

/**
 * IoT Redis Stream 配置 {@link IotAbstractDataSinkConfig} 实现类
 *
 * <AUTHOR>
 */
@Data
public class IotDataSinkRedisStreamConfig extends IotAbstractDataSinkConfig {

    /**
     * Redis 服务器地址
     */
    private String host;
    /**
     * 端口
     */
    private Integer port;
    /**
     * 密码
     */
    private String password;
    /**
     * 数据库索引
     */
    private Integer database;

    /**
     * 主题
     */
    private String topic;
}