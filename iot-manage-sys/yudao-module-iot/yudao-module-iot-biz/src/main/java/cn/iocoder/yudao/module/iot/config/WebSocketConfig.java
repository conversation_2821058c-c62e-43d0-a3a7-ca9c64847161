package cn.iocoder.yudao.module.iot.config;

import cn.iocoder.yudao.module.iot.controller.app.websocket.IotWebSocketController;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket 配置类
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    private final IotWebSocketController iotWebSocketController;

    public WebSocketConfig(IotWebSocketController iotWebSocketController) {
        this.iotWebSocketController = iotWebSocketController;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(iotWebSocketController, "/websocket/iot")
                .setAllowedOrigins("*") // 允许所有来源，生产环境建议限制
                .withSockJS(); // 启用SockJS支持，提供更好的兼容性
    }
} 