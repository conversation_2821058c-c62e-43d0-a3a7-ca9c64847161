package cn.iocoder.yudao.module.iot.controller.app.scene.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Schema(description = "用户 APP - 场景动作创建 Request VO")
@Data
public class IotSceneActionCreateReqVO {

    @Schema(description = "产品ID", example = "10")
    private Long productId;
    @Schema(description = "属性标识", example = "switch")
    private String attr;
    @Schema(description = "属性值", example = "开")
    private String value;

    @Schema(description = "设备ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "设备ID不能为空")
    private Long deviceId;

    @Schema(description = "动作类型：SWITCH-开关，BRIGHTNESS-亮度，COLOR-颜色，TEMPERATURE-温度设置，CUSTOM-自定义指令", requiredMode = Schema.RequiredMode.REQUIRED, example = "SWITCH")
    @NotEmpty(message = "动作类型不能为空")
    private String actionType;

    @Schema(description = "动作值（JSON格式）", example = "{\"status\": \"on\"}")
    private String actionValue;

    @Schema(description = "延迟执行时间（秒）", example = "0")
    private Integer delay;

    @Schema(description = "执行顺序", example = "1")
    private Integer orderNum;

} 