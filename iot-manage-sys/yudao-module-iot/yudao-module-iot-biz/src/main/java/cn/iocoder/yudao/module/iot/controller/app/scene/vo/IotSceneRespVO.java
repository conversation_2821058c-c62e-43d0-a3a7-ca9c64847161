package cn.iocoder.yudao.module.iot.controller.app.scene.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "用户 APP - 场景 Response VO")
@Data
public class IotSceneRespVO {

    @Schema(description = "场景ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "场景名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "回家模式")
    private String name;

    @Schema(description = "场景描述", example = "回家时自动开启客厅灯光和空调")
    private String description;

    @Schema(description = "状态 1启用 0禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "场景类型（auto/manual）", example = "auto")
    private String type;

    @Schema(description = "租户编号", example = "1")
    private Long tenantId;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建者", example = "admin")
    private String creator;

    @Schema(description = "更新者", example = "admin")
    private Integer updater;

    @Schema(description = "是否删除", example = "0")
    private Integer deleted;

    @Schema(description = "创建用户ID", example = "1024")
    private Long userId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "条件列表")
    private List<IotSceneConditionRespVO> conditions;

    @Schema(description = "动作列表")
    private List<IotSceneActionRespVO> actions;

} 