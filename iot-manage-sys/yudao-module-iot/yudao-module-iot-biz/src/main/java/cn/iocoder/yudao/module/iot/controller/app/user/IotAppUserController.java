package cn.iocoder.yudao.module.iot.controller.app.user;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.access.annotation.Secured;
import javax.annotation.security.PermitAll;
import cn.iocoder.yudao.module.iot.controller.app.user.vo.IotUserLoginReqVO;
import cn.iocoder.yudao.module.iot.controller.app.user.vo.IotUserLoginRespVO;
import cn.iocoder.yudao.module.iot.controller.app.user.vo.IotUserRegisterReqVO;
import cn.iocoder.yudao.module.iot.controller.app.user.vo.IotUserRespVO;
import cn.iocoder.yudao.module.iot.service.user.IotUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - IoT用户")
@RestController
@RequestMapping("/iot/user")
@Validated
@Slf4j
//@PreAuthorize("@ss.hasAnyUserType('MEMBER', 'ADMIN')")
public class IotAppUserController {

    @Resource
    private IotUserService iotUserService;

    @PostMapping("/login")
    @Operation(summary = "IoT用户登录")
    @PermitAll
    public CommonResult<IotUserLoginRespVO> login(@Valid @RequestBody IotUserLoginReqVO reqVO) {
        log.info("[IotAppUserController] 用户登录请求，参数：{}", reqVO);
        IotUserLoginRespVO respVO = iotUserService.login(reqVO);
        log.info("[IotAppUserController] 用户登录成功，用户ID：{}", respVO.getId());
        return success(respVO);
    }

    @PostMapping("/register")
    @Operation(summary = "IoT用户注册")
    @PermitAll
    public CommonResult<Boolean> register(@Valid @RequestBody IotUserRegisterReqVO reqVO) {
        log.info("[IotAppUserController] 用户注册请求，参数：{}", reqVO);
        iotUserService.register(reqVO);
        log.info("[IotAppUserController] 用户注册成功，用户名：{}", reqVO.getUsername());
        return success(true);
    }

    @PostMapping("/logout")
    @Operation(summary = "IoT用户退出登录")
    @PreAuthorize("@iotPermissionService.hasPermission('iot:user:logout')")
    public CommonResult<Boolean> logout() {
        log.info("[IotAppUserController] 用户退出登录请求");
        iotUserService.logout();
        log.info("[IotAppUserController] 用户退出登录成功");
        return success(true);
    }

    @GetMapping("/info")
    @Operation(summary = "获取IoT用户信息")
    public CommonResult<IotUserRespVO> getUserInfo() {
        log.info("[IotAppUserController] 获取用户信息请求");
        IotUserRespVO userInfo = iotUserService.getCurrentUserInfo();
        log.info("[IotAppUserController] 获取用户信息成功，用户ID：{}", userInfo.getId());
        return success(userInfo);
    }

    @PutMapping("/update")
    @Operation(summary = "更新IoT用户信息")
    @PreAuthorize("@iotPermissionService.hasPermission('iot:user:update')")
    public CommonResult<Boolean> updateUserInfo(@Valid @RequestBody IotUserRespVO reqVO) {
        log.info("[IotAppUserController] 更新用户信息请求，参数：{}", reqVO);
        iotUserService.updateUserInfo(reqVO);
        log.info("[IotAppUserController] 更新用户信息成功，用户ID：{}", reqVO.getId());
        return success(true);
    }
} 