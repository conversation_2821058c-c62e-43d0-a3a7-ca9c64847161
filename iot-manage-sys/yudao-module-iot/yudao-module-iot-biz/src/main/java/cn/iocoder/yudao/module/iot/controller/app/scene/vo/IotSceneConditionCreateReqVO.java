package cn.iocoder.yudao.module.iot.controller.app.scene.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Schema(description = "用户 APP - 场景条件创建 Request VO")
@Data
public class IotSceneConditionCreateReqVO {

    @Schema(description = "产品ID", example = "17")
    private Long productId;
    @Schema(description = "设备ID", example = "15")
    private Long deviceId;
    @Schema(description = "属性标识", example = "temperature")
    private String attr;
    @Schema(description = "属性值", example = "25")
    private String value;

    @Schema(description = "条件类型：TIMER-定时，TEMPERATURE-温度，HUMIDITY-湿度，LIGHT-光照，MOTION-人体感应，MANUAL-手动触发", requiredMode = Schema.RequiredMode.REQUIRED, example = "TIMER")
    @NotEmpty(message = "条件类型不能为空")
    private String conditionType;

    @Schema(description = "条件值（JSON格式）", example = "{\"time\": \"22:00\", \"days\": [1,2,3,4,5,6,7]}")
    private String conditionValue;

    @Schema(description = "操作符：=, >, <, >=, <=, !=", example = "=")
    private String operator;

    @Schema(description = "优先级", example = "1")
    private Integer priority;

} 