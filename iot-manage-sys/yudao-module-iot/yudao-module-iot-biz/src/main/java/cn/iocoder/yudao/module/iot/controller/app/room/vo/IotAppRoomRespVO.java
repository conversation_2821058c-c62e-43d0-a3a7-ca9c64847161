package cn.iocoder.yudao.module.iot.controller.app.room.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "用户 APP - IoT 房间 Response VO")
@Data
public class IotAppRoomRespVO {

    @Schema(description = "房间编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "房间名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "客厅")
    private String name;

    @Schema(description = "房间类型", example = "living_room")
    private String type;

    @Schema(description = "父级房间ID", example = "1")
    private Long parentId;

    @Schema(description = "所属用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long userId;

    @Schema(description = "房间图标", example = "http://xxx/room.png")
    private String icon;

    @Schema(description = "排序", example = "0")
    private Integer sort;

    @Schema(description = "租户编号", example = "1")
    private Long tenantId;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建者", example = "admin")
    private String creator;

    @Schema(description = "更新者", example = "admin")
    private String updater;

    @Schema(description = "是否删除", example = "0")
    private String deleted;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "子房间列表")
    private List<IotAppRoomRespVO> children;

    @Schema(description = "设备数量")
    private Integer deviceCount;
} 