package cn.iocoder.yudao.module.iot.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import java.time.LocalDateTime;

/**
 * IoT 插件配置 DO
 */
@TableName("iot_plugin_config")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IotPluginConfigDO {
    @TableId
    private Long id;
    private String pluginKey;
    private String name;
    private Integer status;
    private String description;
    private Integer deployType;
    private String fileName;
    private String version;
    private Integer type;
    private String protocol;
    private String configSchema;
    private String config;
    private String script;
    private String creator;
    private LocalDateTime createTime;
    private String updater;
    private LocalDateTime updateTime;
    private Boolean deleted;
    private Long tenantId;
} 