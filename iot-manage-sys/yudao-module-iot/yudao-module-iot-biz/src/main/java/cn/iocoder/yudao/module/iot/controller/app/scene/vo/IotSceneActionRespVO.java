package cn.iocoder.yudao.module.iot.controller.app.scene.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户 APP - 场景动作 Response VO")
@Data
public class IotSceneActionRespVO {

    @Schema(description = "动作ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "场景ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long sceneId;

    @Schema(description = "设备ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long deviceId;

    @Schema(description = "设备名称", example = "客厅灯")
    private String deviceName;

    @Schema(description = "动作类型：SWITCH-开关，BRIGHTNESS-亮度，COLOR-颜色，TEMPERATURE-温度设置，CUSTOM-自定义指令", requiredMode = Schema.RequiredMode.REQUIRED, example = "SWITCH")
    private String actionType;

    @Schema(description = "动作值（JSON格式）", example = "{\"status\": \"on\"}")
    private String actionValue;

    @Schema(description = "延迟执行时间（秒）", example = "0")
    private Integer delay;

    @Schema(description = "执行顺序", example = "1")
    private Integer orderNum;

    @Schema(description = "产品ID", example = "4")
    private Long productId;

    @Schema(description = "产品名称", example = "客厅灯")
    private String productName;

    @Schema(description = "属性标识", example = "switch")
    private String attr;

    @Schema(description = "属性值", example = "开")
    private String value;

    @Schema(description = "租户编号", example = "1")
    private Long tenantId;

    @Schema(description = "创建时间")
    private java.time.LocalDateTime createTime;

    @Schema(description = "更新时间")
    private java.time.LocalDateTime updateTime;

    @Schema(description = "创建者", example = "admin")
    private String creator;

    @Schema(description = "更新者", example = "admin")
    private Integer updater;

    @Schema(description = "是否删除", example = "0")
    private String deleted;

} 