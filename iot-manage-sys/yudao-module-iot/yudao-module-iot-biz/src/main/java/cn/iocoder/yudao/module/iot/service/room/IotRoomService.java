package cn.iocoder.yudao.module.iot.service.room;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.iot.controller.admin.room.vo.IotRoomPageReqVO;
import cn.iocoder.yudao.module.iot.controller.admin.room.vo.IotRoomSaveReqVO;
import cn.iocoder.yudao.module.iot.dal.dataobject.room.IotRoomDO;
import jakarta.validation.Valid;

import java.util.Collection;
import java.util.List;

/**
 * IoT 房间 Service 接口
 *
 * <AUTHOR>
 */
public interface IotRoomService {

    /**
     * 创建房间
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRoom(@Valid IotRoomSaveReqVO createReqVO);

    /**
     * 更新房间
     *
     * @param updateReqVO 更新信息
     */
    void updateRoom(@Valid IotRoomSaveReqVO updateReqVO);

    /**
     * 删除房间
     *
     * @param id 编号
     */
    void deleteRoom(Long id);

    /**
     * 获得房间
     *
     * @param id 编号
     * @return 房间
     */
    IotRoomDO getRoom(Long id);

    /**
     * 获得房间分页
     *
     * @param pageReqVO 分页查询
     * @return 房间分页
     */
    PageResult<IotRoomDO> getRoomPage(IotRoomPageReqVO pageReqVO);

    /**
     * 获得用户的房间列表
     *
     * @param userId 用户编号
     * @return 房间列表
     */
    List<IotRoomDO> getRoomListByUserId(Long userId);

    /**
     * 获得子房间列表
     *
     * @param parentId 父级房间编号
     * @return 房间列表
     */
    List<IotRoomDO> getRoomListByParentId(Long parentId);

    /**
     * 获得用户的子房间列表
     *
     * @param userId   用户编号
     * @param parentId 父级房间编号
     * @return 房间列表
     */
    List<IotRoomDO> getRoomListByUserIdAndParentId(Long userId, Long parentId);

    /**
     * 校验房间是否存在
     *
     * @param id 房间编号
     */
    void validateRoomExists(Long id);

    /**
     * 校验房间是否存在
     *
     * @param ids 房间编号数组
     */
    void validateRoomExists(Collection<Long> ids);

}
