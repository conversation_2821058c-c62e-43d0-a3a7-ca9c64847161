package cn.iocoder.yudao.module.iot.dal.mysql.scene;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.iot.dal.dataobject.scene.IotSceneDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 场景 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface IotSceneMapper extends BaseMapperX<IotSceneDO> {

    default List<IotSceneDO> selectListByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<IotSceneDO>()
                .eq(IotSceneDO::getUserId, userId)
                .orderByDesc(IotSceneDO::getCreateTime));
    }

    default List<IotSceneDO> selectListByStatus(Integer status) {
        return selectList(new LambdaQueryWrapperX<IotSceneDO>()
                .eq(IotSceneDO::getStatus, status)
                .orderByDesc(IotSceneDO::getCreateTime));
    }

} 