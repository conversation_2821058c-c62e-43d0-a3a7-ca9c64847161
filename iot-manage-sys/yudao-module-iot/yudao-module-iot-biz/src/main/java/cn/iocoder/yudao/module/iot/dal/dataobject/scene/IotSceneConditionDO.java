package cn.iocoder.yudao.module.iot.dal.dataobject.scene;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 场景条件 DO
 *
 * <AUTHOR>
 */
@TableName("iot_scene_condition")
@KeySequence("iot_scene_condition_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IotSceneConditionDO extends TenantBaseDO {

    /**
     * 条件ID
     */
    @TableId
    private Long id;
    /**
     * 场景ID
     */
    private Long sceneId;
    /**
     * 条件类型：TIMER-定时，TEMPERATURE-温度，HUMIDITY-湿度，LIGHT-光照，MOTION-人体感应，MANUAL-手动触发
     */
    private String conditionType;
    /**
     * 条件值（JSON格式）
     */
    private String conditionValue;
    /**
     * 操作符：=, >, <, >=, <=, !=
     */
    private String operator;
    /**
     * 优先级
     */
    private Integer priority;
    // 新增字段
    private Long productId;
    private Long deviceId;
    private String attr;
    private String value;
} 