package cn.iocoder.yudao.module.iot.controller.admin.room.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - IoT 房间新增/修改 Request VO")
@Data
public class IotRoomSaveReqVO {

    @Schema(description = "房间编号", example = "1")
    private Long id;

    @Schema(description = "房间名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "客厅")
    @NotEmpty(message = "房间名称不能为空")
    private String name;

    @Schema(description = "房间类型", example = "living_room")
    private String type;

    @Schema(description = "父级房间ID", example = "1")
    private Long parentId;

    @Schema(description = "所属用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long userId;

}
