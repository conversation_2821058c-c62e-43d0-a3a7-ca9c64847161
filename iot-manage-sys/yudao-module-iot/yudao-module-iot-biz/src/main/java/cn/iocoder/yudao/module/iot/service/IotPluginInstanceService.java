package cn.iocoder.yudao.module.iot.service;

import cn.iocoder.yudao.module.iot.dal.dataobject.IotPluginInstanceDO;
import java.util.List;

public interface IotPluginInstanceService {
    Long createPluginInstance(IotPluginInstanceDO instance);
    void updatePluginInstance(IotPluginInstanceDO instance);
    void deletePluginInstance(Long id);
    IotPluginInstanceDO getPluginInstance(Long id);
    List<IotPluginInstanceDO> getPluginInstanceList();
} 